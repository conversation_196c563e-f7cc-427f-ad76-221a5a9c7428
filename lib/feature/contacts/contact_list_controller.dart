import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'contact_model.dart';
import 'contact_repository.dart';

final contactListProvider = StateNotifierProvider<ContactListController,
    AsyncValue<List<ContactModel>>>(
  (ref) => ContactListController(ref),
);

class ContactListController
    extends StateNotifier<AsyncValue<List<ContactModel>>> {
  final Ref ref;
  final _repo = Contactrepository();

  int _currentPage = 1;
  bool _isFetching = false;
  String _searchQuery = '';

  final List<ContactModel> _allContacts = [];

  ContactListController(this.ref) : super(const AsyncValue.loading()) {
    _loadInitial();
  }
  Future<void> _loadInitial() async {
    _currentPage = 1;
    _allContacts.clear();
    await _fetchPage();
  }

  Future<void> _fetchPage() async {
    if (_isFetching) return;
    _isFetching = true;
    try {
      final contacts = await _repo.getContacts(page: _currentPage);
      _allContacts.addAll(contacts);
      _currentPage++;
      _applySearch();
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    } finally {
      _isFetching = false;
    }
  }

  void _applySearch() {
    if (_searchQuery.isEmpty) {
      state = AsyncValue.data([..._allContacts]);
    } else {
      final filtered = _allContacts
          .where(
              (c) => c.name.toLowerCase().contains(_searchQuery.toLowerCase()))
          .toList();
      state = AsyncValue.data(filtered);
    }
  }

  void search(String query) {
    _searchQuery = query;
    _applySearch();
  }

  Future<void> loadMore() async {
    await _fetchPage();
  }
}
